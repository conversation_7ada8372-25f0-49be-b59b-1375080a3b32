// E-commerce Dashboard JavaScript Functionality

// Cart functionality
let cartCount = 3; // Initial cart count

// Add to cart function
function addToCart(button) {
    // Animate button
    button.style.transform = 'scale(0.95)';
    button.innerHTML = '<svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><span>Adding...</span>';

    setTimeout(() => {
        // Update cart count
        cartCount++;
        const cartCountElement = document.querySelector('.cart-icon span');
        if (cartCountElement) {
            cartCountElement.textContent = cartCount;
        }

        // Animate cart
        animateCart();

        // Reset button
        button.style.transform = 'scale(1)';
        button.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><span>Added!</span>';
        button.style.background = 'linear-gradient(135deg, #10b981, #059669)';

        setTimeout(() => {
            button.innerHTML = '<lord-icon src="https://cdn.lordicon.com/ggirntso.json" trigger="hover" stroke="bold" colors="primary:#ffffff,secondary:#ffffff" style="width:16px;height:16px;"></lord-icon><span>Add</span>';
            button.style.background = '';
        }, 2000);
    }, 1000);
}

// Create ripple effect
function createRipple(element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = (rect.width / 2 - size / 2) + 'px';
    ripple.style.top = (rect.height / 2 - size / 2) + 'px';
    ripple.classList.add('ripple-effect');
    
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Header scroll effects
function handleHeaderScroll() {
    // Sticky Header with Scroll Animation (only for dashboard container)
    let lastScrollTop = 0;
    let scrollTimeout;
    const header = document.getElementById('main-header');
    const dashboardContainer = document.querySelector('.h-\\[600px\\].overflow-y-auto');

    function handleDashboardScroll(event) {
        if (!header || !dashboardContainer) return; // Guard clause if elements don't exist

        const scrollTop = dashboardContainer.scrollTop;
        const scrollingDown = scrollTop > lastScrollTop;
        const scrollThreshold = 50; // Start animation after 50px scroll within dashboard

        // Clear existing timeout
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }

        // Throttle scroll events for better performance
        scrollTimeout = setTimeout(() => {
            if (scrollTop > scrollThreshold) {
                // Add compact class when scrolled down within dashboard
                if (!header.classList.contains('header-compact')) {
                    header.classList.add('header-compact');
                }
            } else {
                // Remove compact class when at top of dashboard
                if (header.classList.contains('header-compact')) {
                    header.classList.remove('header-compact');
                }
            }

            lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
        }, 10); // 10ms throttle for smooth animation
    }

    // Add scroll event listener to dashboard container only
    if (dashboardContainer) {
        dashboardContainer.addEventListener('scroll', handleDashboardScroll, { passive: true });
    }

    // Page Header Scroll Effect - Enhanced Instant Edges Slide Inward
    const pageHeader = document.getElementById('page-header');

    function handlePageScroll() {
        if (!pageHeader) return;

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Instant response - no delays or throttling for smooth animation
        if (scrollTop > 30) {
            pageHeader.classList.add('scrolled');
        } else {
            pageHeader.classList.remove('scrolled');
        }
    }

    // Add page scroll event listener for instant response with passive optimization
    window.addEventListener('scroll', handlePageScroll, { passive: true });

    // Initialize dropdown navigation functionality
    initializeDropdownNavigation();
}

// Dropdown Navigation Functionality
function initializeDropdownNavigation() {
    const sharedDropdownContainer = document.getElementById('sharedDropdownContainer');
    const featuresLink = document.querySelector('.features-link');
    const benefitsLink = document.querySelector('.benefits-link');
    const sharedDropdown = document.querySelector('.shared-dropdown');

    // Only initialize if dropdown elements exist
    if (!sharedDropdownContainer || !featuresLink || !benefitsLink || !sharedDropdown) {
        return;
    }

    let currentPanel = null; // Track which panel is currently active

    // Function to reset Features text animation
    function resetFeaturesTextAnimation() {
        const featuresLinks = document.querySelectorAll('.features-panel .dropdown-link');

        // Reset animation state with updated parameters
        featuresLinks.forEach(link => {
            link.style.opacity = '0';
            link.style.filter = 'blur(6px)';
            link.style.transform = 'translateY(30px)';
        });

        // Trigger reflow to ensure reset is applied
        sharedDropdownContainer.offsetHeight;

        // Remove inline styles to let CSS transitions take over
        setTimeout(() => {
            featuresLinks.forEach(link => {
                link.style.removeProperty('opacity');
                link.style.removeProperty('filter');
                link.style.removeProperty('transform');
            });
        }, 10);
    }

    // Function to reset Benefits text animation
    function resetBenefitsTextAnimation() {
        const benefitsLinks = document.querySelectorAll('.benefits-panel .dropdown-link');

        // Reset animation state with updated parameters
        benefitsLinks.forEach(link => {
            link.style.opacity = '0';
            link.style.filter = 'blur(6px)';
            link.style.transform = 'translateY(30px)';
        });

        // Trigger reflow to ensure reset is applied
        sharedDropdownContainer.offsetHeight;

        // Remove inline styles to let CSS transitions take over
        setTimeout(() => {
            benefitsLinks.forEach(link => {
                link.style.removeProperty('opacity');
                link.style.removeProperty('filter');
                link.style.removeProperty('transform');
            });
        }, 10);
    }

    // Function to reset Features text to initial state (for hover out)
    function resetFeaturesTextToInitialState() {
        const featuresLinks = document.querySelectorAll('.features-panel .dropdown-link');

        // Immediately set to initial state without transitions
        featuresLinks.forEach(link => {
            link.style.transition = 'none';
            link.style.opacity = '0';
            link.style.filter = 'blur(6px)';
            link.style.transform = 'translateY(30px)';
        });

        // Force reflow
        sharedDropdownContainer.offsetHeight;
    }

    // Function to reset Benefits text to initial state (for hover out)
    function resetBenefitsTextToInitialState() {
        const benefitsLinks = document.querySelectorAll('.benefits-panel .dropdown-link');

        // Immediately set to initial state without transitions
        benefitsLinks.forEach(link => {
            link.style.transition = 'none';
            link.style.opacity = '0';
            link.style.filter = 'blur(6px)';
            link.style.transform = 'translateY(30px)';
        });

        // Force reflow
        sharedDropdownContainer.offsetHeight;
    }

    // Features hover - show Features panel
    featuresLink.addEventListener('mouseenter', function() {
        // Clean up any fade-out states
        sharedDropdownContainer.classList.remove('fade-out-features', 'fade-out-benefits');

        // Check if Benefits was active (switching case)
        const wasBenefitsActive = sharedDropdownContainer.classList.contains('benefits-active');

        if (wasBenefitsActive) {
            // Switching from Benefits - allow slide animation with proper timing
            requestAnimationFrame(() => {
                sharedDropdownContainer.classList.remove('benefits-active');
                sharedDropdownContainer.classList.add('features-active');
            });
        } else {
            // Set Features active state - smooth transition will handle the slide
            sharedDropdownContainer.classList.add('features-active');
        }
        currentPanel = 'features';

        // Trigger text animations for Features panel
        resetFeaturesTextAnimation();
    });

    // Benefits hover - show Benefits panel
    benefitsLink.addEventListener('mouseenter', function() {
        // Clean up any fade-out states
        sharedDropdownContainer.classList.remove('fade-out-features', 'fade-out-benefits');

        // Check if Features was active (switching case)
        const wasFeaturesActive = sharedDropdownContainer.classList.contains('features-active');

        if (wasFeaturesActive) {
            // Switching from Features - allow slide animation with proper timing
            requestAnimationFrame(() => {
                sharedDropdownContainer.classList.remove('features-active');
                sharedDropdownContainer.classList.add('benefits-active');
            });
        } else {
            // First hover on Benefits - position instantly without slide
            sharedDropdownContainer.style.setProperty('--instant-position', '-50%');
            sharedDropdownContainer.classList.add('benefits-active');
            // Reset after positioning
            setTimeout(() => {
                sharedDropdownContainer.style.removeProperty('--instant-position');
            }, 10);
        }
        currentPanel = 'benefits';

        // Trigger text animations for Benefits panel
        resetBenefitsTextAnimation();
    });

    // Maintain state when hovering dropdown content
    sharedDropdown.addEventListener('mouseenter', function() {
        // Keep the current active state when hovering dropdown content
        // No state changes needed - just maintain current position
    });

    // Clean up classes when leaving the entire container - maintain current panel during fade-out
    sharedDropdownContainer.addEventListener('mouseleave', function() {
        // Determine which panel was active and maintain its position during fade-out
        const wasFeaturesActive = sharedDropdownContainer.classList.contains('features-active');
        const wasBenefitsActive = sharedDropdownContainer.classList.contains('benefits-active');

        // Reset text animations immediately when hovering out
        if (wasFeaturesActive) {
            resetFeaturesTextToInitialState();
        } else if (wasBenefitsActive) {
            resetBenefitsTextToInitialState();
        }

        // Remove active states but add fade-out state to maintain position
        sharedDropdownContainer.classList.remove('features-active', 'benefits-active');

        if (wasFeaturesActive) {
            sharedDropdownContainer.classList.add('fade-out-features');
        } else if (wasBenefitsActive) {
            sharedDropdownContainer.classList.add('fade-out-benefits');
        }

        // Clean up fade-out classes after dropdown is hidden
        setTimeout(() => {
            sharedDropdownContainer.classList.remove('fade-out-features', 'fade-out-benefits');

            // Restore transitions for both panels after fade-out is complete
            const allLinks = document.querySelectorAll('.features-panel .dropdown-link, .benefits-panel .dropdown-link');
            allLinks.forEach(link => {
                link.style.removeProperty('transition');
            });

            currentPanel = null;
        }, 300); // Match the CSS transition duration for opacity
    });
}

// Search functionality
function initializeSearch() {
    // Search functionality (visual feedback only)
    const searchInput = document.querySelector('input[placeholder="Search products..."]');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const productCards = document.querySelectorAll('.product-card');

            productCards.forEach(card => {
                const productName = card.querySelector('h3').textContent.toLowerCase();
                const productDescription = card.querySelector('p').textContent.toLowerCase();

                if (productName.includes(searchTerm) || productDescription.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.opacity = '1';
                } else {
                    card.style.opacity = '0.3';
                }
            });

            // If search is empty, show all products
            if (searchTerm === '') {
                productCards.forEach(card => {
                    card.style.display = 'block';
                    card.style.opacity = '1';
                });
            }
        });
    }
}

// Wishlist functionality
function initializeWishlist() {
    const heartContainers = document.querySelectorAll('.heart-container');
    
    heartContainers.forEach(container => {
        container.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const lordIcon = container.querySelector('lord-icon');
            
            // Toggle wishlist state
            if (container.classList.contains('active')) {
                container.classList.remove('active');
                container.style.borderColor = 'transparent';
                if (lordIcon) {
                    lordIcon.setAttribute('colors', 'primary:#000000,secondary:#000000');
                }
            } else {
                container.classList.add('active');
                container.style.borderColor = '#ef4444';
                if (lordIcon) {
                    lordIcon.setAttribute('colors', 'primary:#ef4444,secondary:#ef4444');
                }
                
                // Add heart beat animation
                container.style.animation = 'heartBeat 0.6s ease';
                setTimeout(() => {
                    container.style.animation = '';
                }, 600);
            }
        });
    });
}

// Star rating functionality
function initializeStarRating() {
    const starRatings = document.querySelectorAll('.star-rating');
    
    starRatings.forEach(rating => {
        const stars = rating.querySelectorAll('.star');
        
        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                // Remove filled class from all stars
                stars.forEach(s => s.classList.remove('filled'));
                
                // Add filled class to clicked star and all previous stars
                for (let i = 0; i <= index; i++) {
                    stars[i].classList.add('filled');
                }
            });
            
            star.addEventListener('mouseenter', () => {
                // Temporarily highlight stars on hover
                stars.forEach((s, i) => {
                    if (i <= index) {
                        s.style.color = '#f59e0b';
                    } else {
                        s.style.color = '#d1d5db';
                    }
                });
            });
            
            star.addEventListener('mouseleave', () => {
                // Reset to original state
                stars.forEach(s => {
                    s.style.color = '';
                });
            });
        });
    });
}

// Category navigation
function initializeCategoryNavigation() {
    // Category hover effects with icons
    document.querySelectorAll('.category-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            const icon = this.querySelector('svg');
            if (icon) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });

        item.addEventListener('mouseleave', function() {
            const icon = this.querySelector('svg');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg)';
            }
        });

        item.addEventListener('click', (e) => {
            e.preventDefault();

            // Remove active state from all categories
            document.querySelectorAll('.category-item').forEach(cat => cat.classList.remove('active'));

            // Add active state to clicked category
            item.classList.add('active');

            // Filter products based on category (placeholder functionality)
            const categoryName = item.textContent.trim();
            console.log(`Filtering products for category: ${categoryName}`);

            // Add visual feedback
            item.style.transform = 'translateX(5px)';
            setTimeout(() => {
                item.style.transform = '';
            }, 200);
        });
    });
}

// Enhanced smooth scrolling for navigation links
function initializeSmoothScrolling() {
    // Enhanced smooth scrolling for all navigation links including dropdown links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const pageHeader = document.getElementById('page-header');
                const headerHeight = pageHeader ? pageHeader.offsetHeight : 0;
                const targetPosition = target.offsetTop - headerHeight - 20;

                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Legacy support for page-nav-item class
    document.querySelectorAll('.page-nav-item').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const pageHeader = document.getElementById('page-header');
                const headerHeight = pageHeader ? pageHeader.offsetHeight : 0;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Cart animation function
function animateCart() {
    const cartIcon = document.querySelector('.cart-icon lord-icon');

    if (cartIcon) {
        // Method 1: Try using the play method if available
        if (cartIcon.playerInstance && cartIcon.playerInstance.play) {
            try {
                cartIcon.playerInstance.stop();
                cartIcon.playerInstance.play();
            } catch (e) {
                console.log('Method 1 failed, trying method 2');
            }
        }

        // Method 2: Dispatch a custom event to trigger animation
        const event = new Event('mouseenter');
        cartIcon.dispatchEvent(event);

        setTimeout(() => {
            const leaveEvent = new Event('mouseleave');
            cartIcon.dispatchEvent(leaveEvent);
        }, 50);
    }
}

// Additional functionality
function initializeAdditionalFeatures() {
    // Quantity selector functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('quantity-btn')) {
            const quantitySpan = e.target.parentElement.querySelector('span');
            let quantity = parseInt(quantitySpan.textContent);

            if (e.target.textContent === '+') {
                quantity++;
            } else if (e.target.textContent === '-' && quantity > 1) {
                quantity--;
            }

            quantitySpan.textContent = quantity;
        }
    });

    // Smooth scrolling for container
    const scrollContainer = document.querySelector('.overflow-y-auto');
    if (scrollContainer) {
        scrollContainer.style.scrollBehavior = 'smooth';
    }

    // Color selector functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('w-4') && e.target.classList.contains('h-4') && e.target.classList.contains('rounded-full')) {
            // Remove selection from siblings
            const siblings = e.target.parentElement.querySelectorAll('.w-4.h-4.rounded-full');
            siblings.forEach(sibling => {
                sibling.style.borderColor = '#d1d5db';
                sibling.style.borderWidth = '2px';
            });

            // Add selection to clicked color
            e.target.style.borderColor = '#3674B5';
            e.target.style.borderWidth = '3px';
        }
    });
}

// Mobile Menu Functionality
function initializeMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const hamburgerIcon = document.getElementById('hamburger-icon');
    const closeIcon = document.getElementById('close-icon');
    const body = document.body;

    // Only initialize if mobile menu elements exist
    if (!mobileMenuButton || !mobileMenu) {
        return;
    }

    let isMenuOpen = false;

    // Toggle mobile menu
    function toggleMobileMenu() {
        isMenuOpen = !isMenuOpen;

        if (isMenuOpen) {
            mobileMenu.classList.add('open');
            hamburgerIcon.classList.add('hidden');
            closeIcon.classList.remove('hidden');
            body.style.overflow = 'hidden'; // Prevent background scrolling
        } else {
            mobileMenu.classList.remove('open');
            hamburgerIcon.classList.remove('hidden');
            closeIcon.classList.add('hidden');
            body.style.overflow = ''; // Restore scrolling

            // Close all open dropdowns when closing menu
            const openDropdowns = mobileMenu.querySelectorAll('.mobile-dropdown-content.open');
            const activeToggles = mobileMenu.querySelectorAll('.mobile-dropdown-toggle.active');

            openDropdowns.forEach(dropdown => dropdown.classList.remove('open'));
            activeToggles.forEach(toggle => toggle.classList.remove('active'));
        }
    }

    // Mobile menu button click handler
    mobileMenuButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleMobileMenu();
    });

    // Mobile dropdown toggles
    const dropdownToggles = mobileMenu.querySelectorAll('.mobile-dropdown-toggle');

    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const content = this.parentElement.querySelector('.mobile-dropdown-content');
            const isOpen = content.classList.contains('open');

            // Close all other dropdowns
            dropdownToggles.forEach(otherToggle => {
                if (otherToggle !== this) {
                    otherToggle.classList.remove('active');
                    const otherContent = otherToggle.parentElement.querySelector('.mobile-dropdown-content');
                    if (otherContent) {
                        otherContent.classList.remove('open');
                    }
                }
            });

            // Toggle current dropdown
            if (isOpen) {
                this.classList.remove('active');
                content.classList.remove('open');
            } else {
                this.classList.add('active');
                content.classList.add('open');
            }
        });
    });

    // Close menu when clicking on navigation links
    const mobileNavLinks = mobileMenu.querySelectorAll('.mobile-nav-item, .mobile-dropdown-item');

    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Close the mobile menu when a link is clicked
            if (isMenuOpen) {
                toggleMobileMenu();
            }
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (isMenuOpen && !mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
            toggleMobileMenu();
        }
    });

    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isMenuOpen) {
            toggleMobileMenu();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        // Close mobile menu if window becomes desktop size
        if (window.innerWidth >= 768 && isMenuOpen) {
            toggleMobileMenu();
        }
    });

    console.log('Mobile menu initialized successfully!');
}

// Initialize all functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    handleHeaderScroll();
    initializeSearch();
    initializeWishlist();
    initializeStarRating();
    initializeCategoryNavigation();
    initializeSmoothScrolling();
    initializeAdditionalFeatures();
    initializeMobileMenu(); // Add mobile menu initialization

    // Initialize scroll-triggered animations
    initializeScrollAnimations();

    // Add visibility change listener for performance
    document.addEventListener('visibilitychange', handleVisibilityChange);

    console.log('E-commerce dashboard initialized successfully!');
});

// Add CSS for ripple effect
const rippleCSS = `
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

// Inject ripple CSS
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);

// ========================================
// SCROLL-TRIGGERED ANIMATIONS WITH INTERSECTION OBSERVER
// ========================================

// Enhanced Intersection Observer for scroll-triggered animations
function initializeScrollAnimations() {
    // Check if Intersection Observer is supported
    if (!('IntersectionObserver' in window)) {
        // Fallback: show all elements immediately
        document.querySelectorAll('.animate-on-scroll, .animate-slide-left, .animate-slide-right, .animate-scale-up').forEach(el => {
            el.classList.add('visible');
        });
        return;
    }

    // Intersection Observer options for optimal performance
    const observerOptions = {
        root: null, // Use viewport as root
        rootMargin: '-10% 0px -10% 0px', // Trigger when element is 10% into viewport
        threshold: [0, 0.1, 0.3, 0.5] // Multiple thresholds for smooth triggering
    };

    // Create the observer with performance optimizations and reset functionality
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const element = entry.target;

            // Use requestAnimationFrame for smooth 60fps animations
            if (entry.isIntersecting && entry.intersectionRatio >= 0.1) {
                requestAnimationFrame(() => {
                    element.classList.add('visible');
                });
            } else {
                // Reset animation when element goes out of view for repeatable animations
                requestAnimationFrame(() => {
                    element.classList.remove('visible');
                });
            }
        });
    }, observerOptions);

    // Find all elements that should be animated
    const animatedElements = document.querySelectorAll(
        '.animate-on-scroll, .animate-slide-left, .animate-slide-right, .animate-scale-up'
    );

    // Start observing each element
    animatedElements.forEach(element => {
        // Ensure element starts in hidden state
        element.classList.remove('visible');
        observer.observe(element);
    });

    // Store observer reference for cleanup
    window.scrollAnimationObserver = observer;
}

// Cleanup function for scroll animations
function cleanupScrollAnimations() {
    if (window.scrollAnimationObserver) {
        window.scrollAnimationObserver.disconnect();
        window.scrollAnimationObserver = null;
    }
}

// Handle page visibility changes for performance
function handleVisibilityChange() {
    if (document.hidden) {
        // Pause animations when page is hidden
        cleanupScrollAnimations();
    } else {
        // Resume animations when page becomes visible
        setTimeout(initializeScrollAnimations, 100);
    }
}

// ========================================
// MIGRATED ADVANCED ANIMATIONS FROM TESTING.HTML
// ========================================

// ========================================
// 60FPS PERFORMANCE OPTIMIZED ANIMATIONS
// ========================================

// Optimized Multi-Stage Animated Number Counter (60 FPS)
function animateNumber() {
  const numberElement = document.getElementById('animatedNumber');
  if (!numberElement) return;

  // Animation stages: [startValue, endValue, duration, pauseDuration]
  const stages = [
    { start: 2582, end: 3723, duration: 2000, pause: 1500 }, // Stage 1 → 2
    { start: 3723, end: 4660, duration: 2500, pause: 0 }     // Stage 2 → 3 (final)
  ];

  let currentStage = 0;
  let isAnimating = false;
  let animationId = null;

  // Pre-calculate random numbers for smooth 60fps performance
  const randomCache = [];
  for (let i = 0; i < 1000; i++) {
    randomCache.push(Math.random());
  }
  let randomIndex = 0;

  function getNextRandom() {
    randomIndex = (randomIndex + 1) % randomCache.length;
    return randomCache[randomIndex];
  }

  function animateStage(stage) {
    return new Promise((resolve) => {
      const startValue = stage.start;
      const endValue = stage.end;
      const duration = stage.duration;
      const startTime = performance.now(); // Use high-resolution timer
      isAnimating = true;

      let lastDisplayValue = startValue;
      let frameCount = 0;

      function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Optimized easing function
        const easeInOutCubic = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        // Calculate current value with integer precision
        const currentValue = Math.floor(startValue + (endValue - startValue) * easeInOutCubic);

        // Optimized spinning effect (reduced frequency for 60fps)
        let displayValue = currentValue;
        frameCount++;

        // Spinning logic optimized for performance
        if (progress > 0.1 && progress < 0.85 && frameCount % 4 === 0 && getNextRandom() < 0.3) {
          const range = endValue - startValue;
          const spinRange = Math.floor(range * 0.25); // Reduced range for stability
          const baseValue = startValue + Math.floor(range * progress);
          displayValue = baseValue + Math.floor(getNextRandom() * spinRange) - Math.floor(spinRange / 2);

          // Ensure displayValue is within reasonable bounds
          displayValue = Math.max(startValue, Math.min(endValue, displayValue));
        }

        // Only update DOM if value changed (performance optimization)
        if (displayValue !== lastDisplayValue) {
          // Use efficient number formatting
          numberElement.textContent = displayValue.toLocaleString('en-US');
          lastDisplayValue = displayValue;
        }

        // Continue animation if not finished
        if (progress < 1) {
          animationId = requestAnimationFrame(updateNumber);
        } else {
          // Ensure final value is correct
          numberElement.textContent = endValue.toLocaleString('en-US');
          isAnimating = false;
          animationId = null;

          // Pause before next stage
          setTimeout(() => {
            resolve();
          }, stage.pause);
        }
      }

      // Start the animation loop
      animationId = requestAnimationFrame(updateNumber);
    });
  }

  async function runAllStages() {
    // Start with initial value
    numberElement.textContent = stages[0].start.toLocaleString('en-US');

    // Wait initial delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Run each stage sequentially
    for (let i = 0; i < stages.length; i++) {
      await animateStage(stages[i]);
    }
  }

  // Cleanup function for proper resource management
  function cleanup() {
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }
    isAnimating = false;
  }

  // Handle page visibility changes for performance
  document.addEventListener('visibilitychange', function() {
    if (document.hidden && isAnimating) {
      cleanup();
    }
  });

  runAllStages();
}

// Inventory Dashboard Animation
function startInventoryAnimations() {
  console.log('Starting inventory animations...');

  // Define target widths
  const targets = {
    'electronics': 78,
    'clothing': 34,
    'home': 12
  };

  // Reset progress bars
  const progressBars = document.querySelectorAll('.inventory-progress-fill');
  progressBars.forEach(bar => {
    bar.style.width = '1%';
    bar.style.transition = 'none'; // Disable transition for reset
    // Force reflow
    bar.offsetHeight;
  });

  // Reset counters
  const counters = document.querySelectorAll('.inventory-counter');
  counters.forEach(counter => {
    counter.textContent = '0';
  });

  // Start animations immediately (no delay)
  setTimeout(() => {
    progressBars.forEach(bar => {
      // Re-enable transition
      bar.style.transition = 'width 2.5s cubic-bezier(0.4, 0, 0.2, 1)';

      // Find the target width for this bar
      let targetWidth = 1;
      if (bar.classList.contains('electronics')) targetWidth = targets.electronics;
      else if (bar.classList.contains('clothing')) targetWidth = targets.clothing;
      else if (bar.classList.contains('home')) targetWidth = targets.home;

      console.log(`Animating ${bar.className} to ${targetWidth}%`);
      bar.style.width = targetWidth + '%';
    });

    // Start number animations
    animateInventoryCounters();
  }, 10); // Minimal delay just for browser reflow
}

// Number counter animation for inventory
function animateInventoryCounters() {
  const counters = document.querySelectorAll('.inventory-counter');

  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2500; // 2.5 seconds to match progress bars
    const startTime = performance.now();

    function updateCounter(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      const currentValue = Math.floor(target * easeOutCubic);

      counter.textContent = currentValue;

      if (progress < 1) {
        requestAnimationFrame(updateCounter);
      } else {
        counter.textContent = target;
      }
    }

    // Start immediately with progress bars
    requestAnimationFrame(updateCounter);
  });
}

// Text animate function - slideLeft by character
function animateTextByCharacter(elementId, delay = 0) {
  const element = document.getElementById(elementId);
  if (!element) return;

  const text = element.textContent;
  element.innerHTML = ''; // Clear the original text

  // Split text into characters and wrap each in a span
  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const span = document.createElement('span');
    span.className = 'text-animate-char';
    span.textContent = char === ' ' ? '\u00A0' : char; // Use non-breaking space
    span.style.animationDelay = `${delay + (i * 0.05)}s`; // 50ms stagger between characters
    element.appendChild(span);
  }
}

// ========================================
// AI CHAT INTERFACE SIMULATION
// ========================================

// AI Chat Simulation
class ChatSimulation {
  constructor() {
    this.chatContainer = document.getElementById('chatContainer');
    this.thinkingContainer = document.getElementById('thinkingContainer');
    this.typingIndicator = document.getElementById('typingIndicator');
    this.thinkingStage = document.getElementById('thinkingStage');
    this.messageIndex = 0;
    this.isRunning = false;

    // Conversation flow
    this.conversation = [
      { type: 'user', text: 'Hi! I need help with my order #12345', delay: 1000 },
      { type: 'ai', text: 'Hello! I\'d be happy to help you with order #12345. Let me look that up for you.', delay: 2500, thinking: ['Processing...', 'Looking up order...', 'Checking status...'] },
      { type: 'ai', text: 'I found your order! It was shipped yesterday and should arrive tomorrow by 2 PM.', delay: 2000 },
      { type: 'user', text: 'Great! Can I change the delivery address?', delay: 1500 },
      { type: 'ai', text: 'I can help with that! Since it\'s already in transit, I\'ll contact the carrier to update the address.', delay: 3000, thinking: ['Checking options...', 'Contacting carrier...', 'Updating details...'] },
      { type: 'ai', text: 'Done! Your package will be delivered to the new address. You\'ll get a confirmation email shortly.', delay: 2000 },
      { type: 'user', text: 'Perfect! Thank you so much! 😊', delay: 1000 },
      { type: 'ai', text: 'You\'re welcome! Is there anything else I can help you with today?', delay: 1500 }
    ];
  }

  async startSimulation() {
    if (this.isRunning) return;
    this.isRunning = true;
    this.messageIndex = 0;
    this.chatContainer.innerHTML = '';

    for (let i = 0; i < this.conversation.length; i++) {
      const message = this.conversation[i];
      await this.delay(message.delay);

      if (message.type === 'ai' && message.thinking) {
        await this.showThinkingAnimation(message.thinking);
      } else if (message.type === 'ai') {
        await this.showTypingIndicator();
      }

      await this.addMessage(message);
    }

    // Mark simulation as complete
    this.isRunning = false;
  }

  async showThinkingAnimation(stages) {
    this.thinkingContainer.style.display = 'block';

    for (let stage of stages) {
      this.thinkingStage.textContent = stage;
      await this.delay(800);
    }

    this.thinkingContainer.style.display = 'none';
  }

  async showTypingIndicator() {
    this.typingIndicator.style.display = 'block';
    await this.delay(1000);
    this.typingIndicator.style.display = 'none';
  }

  async addMessage(message) {
    const messageElement = document.createElement('div');
    messageElement.className = `message-bubble flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`;

    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    if (message.type === 'user') {
      messageElement.innerHTML = `
        <div class="max-w-[80%]">
          <div class="rounded-lg px-3 py-2 text-xs text-white gradient-brand-message">
            ${message.text}
          </div>
          <div class="text-[9px] text-gray-400 mt-1 text-right message-timestamp">${timestamp}</div>
        </div>
      `;
    } else {
      messageElement.innerHTML = `
        <div class="flex items-start space-x-2 max-w-[80%]">
          <div class="w-4 h-4 rounded-full mt-1 flex-shrink-0 gradient-ai-avatar">
            <svg class="w-2 h-2 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
            </svg>
          </div>
          <div>
            <div class="bg-gray-100 rounded-lg px-3 py-2 text-xs text-gray-800">
              ${message.text}
            </div>
            <div class="text-[9px] text-gray-400 mt-1 message-timestamp">${timestamp}</div>
          </div>
        </div>
      `;
    }

    this.chatContainer.appendChild(messageElement);
    this.scrollToBottom();

    // Small delay for animation
    await this.delay(100);
  }

  scrollToBottom() {
    this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// ========================================
// EVENT HANDLER CONVERSIONS
// ========================================

// Enhanced button hover effects (converted from inline handlers)
function initializeEnhancedButtonEffects() {
  // Convert onmouseover/onmouseout to proper event listeners
  const brandButtons = document.querySelectorAll('button[class*="bg-brand-blue"], button[style*="#3674B5"]');

  brandButtons.forEach(button => {
    // Store original background for restoration
    const originalBg = button.style.backgroundColor || '#3674B5';

    button.addEventListener('mouseenter', function() {
      this.style.backgroundColor = 'black';
    });

    button.addEventListener('mouseleave', function() {
      this.style.backgroundColor = originalBg;
    });
  });
}

// Enhanced add to cart functionality (converted from inline onclick)
function initializeEnhancedAddToCart() {
  // Find all buttons that would have onclick="addToCart(this)"
  const addToCartButtons = document.querySelectorAll('button[class*="btn-primary"]');

  addToCartButtons.forEach(button => {
    // Remove any existing onclick handlers
    button.removeAttribute('onclick');

    // Add proper event listener
    button.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();

      // Call the existing addToCart function
      addToCart(this);
    });
  });
}

// ========================================
// ENHANCED INITIALIZATION
// ========================================

// Enhanced page load initialization
function initializeAdvancedAnimations() {
  console.log('Initializing advanced animations...');

  // Start inventory animations
  setTimeout(() => {
    startInventoryAnimations();
    // Start text animation immediately with other animations
    animateTextByCharacter('lowStockText', 0); // No delay
  }, 100); // Start immediately with minimal delay

  // Initialize chat simulation
  const chatSim = new ChatSimulation();
  setTimeout(() => {
    chatSim.startSimulation();
  }, 1500); // Start chat after 1.5 seconds
}

// Enhanced number animation initialization
function initializeNumberAnimations() {
  // Start animation when page loads
  setTimeout(() => {
    animateNumber();
  }, 500); // Start after 500ms delay
}

// ========================================
// COORDINATED INITIALIZATION
// ========================================

// Enhanced DOMContentLoaded handler that coordinates all functionality
document.addEventListener('DOMContentLoaded', () => {
  // Initialize existing e-commerce functionality
  handleHeaderScroll();
  initializeSearch();
  initializeWishlist();
  initializeStarRating();
  initializeCategoryNavigation();
  initializeSmoothScrolling();
  initializeAdditionalFeatures();

  // Initialize enhanced button effects and event handlers
  initializeEnhancedButtonEffects();
  initializeEnhancedAddToCart();

  // Initialize Platform Impact Showcase
  initializePlatformImpactShowcase();

  // Initialize Dashboard Disclaimer Overlay
  initializeDashboardDisclaimer();

  console.log('E-commerce dashboard initialized successfully!');
});

// Enhanced window load handler for animations
window.addEventListener('load', () => {
  console.log('Page loaded, starting advanced animations...');

  // Initialize number animations
  initializeNumberAnimations();

  // Initialize advanced animations
  initializeAdvancedAnimations();

  console.log('Advanced animations initialized successfully!');
});

// ========================================
// PERFORMANCE OPTIMIZATIONS
// ========================================

// Handle page visibility changes for performance optimization
document.addEventListener('visibilitychange', function() {
  if (document.hidden) {
    // Pause animations when page is hidden
    console.log('Page hidden - optimizing performance');
  } else {
    // Resume animations when page is visible
    console.log('Page visible - resuming animations');
  }
});

// Optimize animations for reduced motion preference
if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
  console.log('Reduced motion preference detected - animations will be simplified');
}

// ========================================
// DASHBOARD DISCLAIMER OVERLAY MODULE
// ========================================

// Initialize Dashboard Disclaimer Overlay
function initializeDashboardDisclaimer() {
    const disclaimerOverlay = document.getElementById('dashboardDisclaimer');
    const continueBtn = document.getElementById('disclaimerContinueBtn');
    const confirmationMessage = document.getElementById('confirmationMessage');

    // Only initialize if disclaimer elements exist
    if (!disclaimerOverlay || !continueBtn || !confirmationMessage) {
        return;
    }

    // Initialize info icon hover functionality after initial animation
    const infoIcon = disclaimerOverlay.querySelector('lord-icon');
    if (infoIcon) {
        // Wait for initial "in" animation to complete (1200ms delay + animation duration)
        setTimeout(() => {
            // Enable hover trigger after initial animation
            infoIcon.setAttribute('trigger', 'hover');
            infoIcon.removeAttribute('delay');
            infoIcon.removeAttribute('state');
        }, 2500); // 1200ms delay + ~1300ms for animation completion
    }

    // Handle continue button click with fade out animation
    continueBtn.addEventListener('click', function(e) {
        e.preventDefault();

        // Add click feedback (works for both button and anchor elements)
        this.style.transform = 'scale(0.98)';

        setTimeout(() => {
            // Fade out the disclaimer overlay
            disclaimerOverlay.style.opacity = '0';
            disclaimerOverlay.style.visibility = 'hidden';

            // Show confirmation message after disclaimer fades out
            setTimeout(() => {
                showConfirmationMessage();
            }, 600); // Wait for fade-out transition to complete
        }, 150); // Brief delay for click feedback
    });

    // Handle keyboard navigation (Enter and Space)
    continueBtn.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
        }
    });

    // Show confirmation message function
    function showConfirmationMessage() {
        confirmationMessage.classList.add('show');

        // Trigger the checkmark animation manually
        const checkmarkIcon = confirmationMessage.querySelector('lord-icon');
        if (checkmarkIcon) {
            // Reset and trigger the animation
            setTimeout(() => {
                checkmarkIcon.setAttribute('trigger', 'click');
                checkmarkIcon.click();
                // Reset back to 'in' trigger after animation
                setTimeout(() => {
                    checkmarkIcon.setAttribute('trigger', 'in');
                }, 100);
            }, 100);
        }

        // Hide confirmation message after 3.5 seconds
        setTimeout(() => {
            confirmationMessage.classList.remove('show');
        }, 3500);
    }

    // Handle escape key to close disclaimer
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !disclaimerOverlay.classList.contains('hidden')) {
            continueBtn.click();
        }
    });

    // Focus management for accessibility
    function trapFocus(element) {
        const focusableElements = element.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstFocusableElement = focusableElements[0];
        const lastFocusableElement = focusableElements[focusableElements.length - 1];

        element.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusableElement) {
                        lastFocusableElement.focus();
                        e.preventDefault();
                    }
                } else {
                    if (document.activeElement === lastFocusableElement) {
                        firstFocusableElement.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    }

    // Apply focus trapping to disclaimer overlay
    if (!disclaimerOverlay.classList.contains('hidden')) {
        trapFocus(disclaimerOverlay);
        // Set initial focus to continue button
        setTimeout(() => {
            continueBtn.focus();
        }, 100);
    }

    console.log('Dashboard disclaimer overlay initialized successfully!');
}

// ========================================
// PLATFORM IMPACT SHOWCASE MODULE
// ========================================

// Initialize Platform Impact Showcase functionality
function initializePlatformImpactShowcase() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');

                // Trigger counter animation for metric cards
                const counter = entry.target.querySelector('.counter');
                if (counter) {
                    animateCounter(counter);
                }
            } else {
                // Reset animation when element goes out of view for repeatable animations
                entry.target.classList.remove('visible');

                // Reset counter if it exists
                const counter = entry.target.querySelector('.counter');
                if (counter && counter.getAttribute('data-target')) {
                    counter.textContent = '0';
                }
            }
        });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Enhanced counter animation with realistic easing
function animateCounter(element) {
    const target = parseFloat(element.getAttribute('data-target'));
    const duration = 2500; // 2.5 seconds for more dramatic effect
    const startTime = performance.now();

    // Smooth easing function - continuous easeOutQuart with gradual deceleration
    function easeOutQuart(t) {
        // Custom curve: fast start, gradual slowdown, dramatic final deceleration
        // This creates a smooth continuous curve without jumps
        return 1 - Math.pow(1 - t, 4);
    }

    // Alternative smoother easing - easeOutExpo but continuous
    function smoothEaseOut(t) {
        if (t === 1) return 1;
        // Smooth exponential decay - no discontinuities
        return 1 - Math.pow(2, -8 * t);
    }

    function updateCounter(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Apply smooth easing - no jumps or stops
        const easedProgress = smoothEaseOut(progress);
        const current = easedProgress * target;

        // Handle different number formats
        if (target === 99.9) {
            // Platform uptime with decimal
            element.textContent = current.toFixed(1);
        } else if (target >= 10) {
            // Larger numbers (15, 40) - show smooth progression
            element.textContent = Math.floor(current);
        } else {
            // Small numbers (2) - show decimal during animation for smoothness
            if (progress < 0.95) {
                element.textContent = current.toFixed(1);
            } else {
                element.textContent = Math.floor(current);
            }
        }

        // Continue animation or finish
        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        } else {
            // Ensure final value is exact
            if (target === 99.9) {
                element.textContent = target.toFixed(1);
            } else {
                element.textContent = Math.floor(target);
            }

            // Add subtle "settle" effect
            element.style.transform = 'scale(1.05)';
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 150);
        }
    }

    // Start the animation
    requestAnimationFrame(updateCounter);
}

/* ===== FEATURE SLIDESHOW MODULE ===== */
// Initialize Feature Slideshow Component
function initializeFeatureSlideshow() {
    // Check if slideshow elements exist on the page
    const featureListContainer = document.getElementById('feature-list');
    const featurePlaceholder = document.getElementById('feature-placeholder');
    const imageSkeleton = document.getElementById('image-skeleton');

    // Only initialize if slideshow elements are present
    if (!featureListContainer || !featurePlaceholder || !imageSkeleton) {
        return; // Exit if slideshow elements not found
    }

    // --- DATA ---
    // An array of objects containing the data for each feature slide.
    // This makes it easy to add, remove, or change features.
    const features = [
        {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>`,
            title: 'Launch Your Store',
            description: 'Create your professional e-commerce store in minutes with our intuitive setup wizard. Choose from beautiful templates and customize your brand identity effortlessly.',
            placeholder: 'Store Setup Dashboard'
        },
        {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/><polyline points="3.27 6.96 12 12.01 20.73 6.96"/><line x1="12" x2="12" y1="22.08" y2="12"/></svg>`,
            title: 'Smart Inventory Management',
            description: 'Balance stock levels efficiently without tying up cash in excess inventory. Track products across categories with real-time updates and automated alerts.',
            placeholder: 'Inventory Control Center'
        },
        {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8"><path d="M18 20V10"/><path d="M12 20V4"/><path d="M6 20v-6"/></svg>`,
            title: 'Sales Analytics & Insights',
            description: 'Track revenue, monitor customer behavior, and make data-driven decisions to grow your store. Get actionable insights from comprehensive sales reports.',
            placeholder: 'Analytics Dashboard'
        },
        {
            icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-8 h-8"><path d="M22 12h-4l-3 9L9 3l-3 9H2"/></svg>`,
            title: 'Scale Your Business',
            description: 'Grow sustainably with AI-driven tools that adapt to your business needs. From small startups to established retailers, scale confidently with Sentio.',
            placeholder: 'Growth Management Hub'
        }
    ];

    // --- CONFIGURATION ---
    const slideDuration = 5000; // 5 seconds per slide
    let currentIndex = 0;
    let slideInterval; // Store interval reference for pause/resume functionality

    // --- INITIALIZATION ---
    // Function to dynamically create the feature items from the data array
    function initializeFeatureList() {
        let featureHTML = '';
        features.forEach((feature, index) => {
            featureHTML += `
                <div class="feature-item relative space-x-6" data-index="${index}">
                    <!-- Progress Bar Container -->
                    <div class="progress-container absolute left-0 top-0 h-full bg-gray-200 rounded-full overflow-hidden">
                        <div class="progress-fill w-full"></div>
                    </div>

                    <!-- Icon and Content with perfect alignment -->
                    <div class="pl-2 flex-shrink-0 text-gray-400 flex items-center" style="width: 40px; height: 40px;">
                        ${feature.icon}
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="feature-title font-bold text-xl text-gray-600 leading-tight">${feature.title}</h3>
                        <p class="feature-description mt-2 text-gray-500 opacity-0 text-sm leading-relaxed">${feature.description}</p>
                    </div>
                </div>
            `;
        });
        featureListContainer.innerHTML = featureHTML;

        // Add click event listeners to feature items
        addClickListeners();
    }

    // --- CLICK FUNCTIONALITY ---
    // Function to add click event listeners to feature items
    function addClickListeners() {
        const allItems = document.querySelectorAll('.feature-item');
        allItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                // Only respond to clicks on non-active items
                if (!item.classList.contains('active')) {
                    goToSlide(index);
                }
            });
        });
    }

    // Function to navigate to a specific slide
    function goToSlide(index) {
        // Update current index and show the slide
        currentIndex = index;
        showSlide(currentIndex);

        // Immediately restart the timer with a fresh countdown
        startSlideshow(); // This will reset the timer completely
    }

    // --- CORE LOGIC ---
    // Function to show a specific slide - enhanced with smooth collapse animations
    function showSlide(index) {
        const allItems = document.querySelectorAll('.feature-item');

        // Use requestAnimationFrame for smooth animations
        requestAnimationFrame(() => {
            allItems.forEach((item, itemIndex) => {
                const fill = item.querySelector('.progress-fill');
                const title = item.querySelector('.feature-title');
                const icon = item.querySelector('svg');
                const description = item.querySelector('.feature-description');

                // Only animate items that are changing state - keep inactive items stationary
                if (itemIndex !== index) {
                    // Only apply collapse animation to currently active item becoming inactive
                    if (item.classList.contains('active')) {
                        // Step 1: Start collapse animation for the previously active item
                        item.classList.add('collapsing');
                        description.classList.add('collapsing');

                        // Step 2: Reset progress and styling immediately for smooth transition
                        fill.style.height = '0%';
                        title.classList.remove('text-gray-900');
                        icon.style.color = ''; // Reset to default
                        title.classList.add('text-gray-600');
                        icon.classList.add('text-gray-400');
                        description.classList.add('opacity-0');

                        // Step 3: Complete collapse after animation starts
                        requestAnimationFrame(() => {
                            item.classList.remove('active');
                            // Remove collapsing class after professional animation completes
                            setTimeout(() => {
                                item.classList.remove('collapsing');
                                description.classList.remove('collapsing');
                            }, 450); // Match optimized CSS transition duration
                        });
                    }
                    // Already inactive items remain untouched - no movement or jumping
                } else {
                    // Enhanced activation for current item
                    // Remove any collapse states first
                    item.classList.remove('collapsing');
                    description.classList.remove('collapsing');

                    // Activate with smooth expansion
                    title.classList.add('text-gray-900');
                    title.classList.remove('text-gray-600');
                    icon.style.color = '#3674B5'; // Use brand color
                    icon.classList.remove('text-gray-400');
                    description.classList.remove('opacity-0');
                    item.classList.add('active');

                    // Reset progress bar height first
                    fill.style.height = '0%';

                    // Start progress animation with optimized timing
                    requestAnimationFrame(() => {
                        fill.style.height = '100%';
                    });
                }
            });
        });

        // Update the placeholder text with smooth animation
        // Step 1: Start loading state with fade-out
        featurePlaceholder.style.opacity = '0';

        // Step 2: Update text content after fade-out
        setTimeout(() => {
            featurePlaceholder.querySelector('p').textContent = features[index].placeholder;

            // Step 3: Fade back in with new content
            featurePlaceholder.style.opacity = '1';
        }, 200); // Brief fade duration
    }

    // --- EVENT LOOP ---
    // Function to advance to the next slide
    function nextSlide() {
        currentIndex = (currentIndex + 1) % features.length;
        showSlide(currentIndex);
    }

    // Function to start/restart the automatic slideshow
    function startSlideshow() {
        clearInterval(slideInterval); // Clear any existing interval
        slideInterval = setInterval(nextSlide, slideDuration);
    }

    // --- PAGE VISIBILITY HANDLING ---
    // Function to handle tab visibility changes (fixes alt+tab issues)
    function handleVisibilityChange() {
        if (document.hidden) {
            // Tab is hidden - pause slideshow
            clearInterval(slideInterval);
        } else {
            // Tab is visible again - reset and restart slideshow
            resetSlideshow();
        }
    }

    // Function to completely reset the slideshow state
    function resetSlideshow() {
        // Clear any existing interval
        clearInterval(slideInterval);

        // Reset all progress bars and states
        const allItems = document.querySelectorAll('.feature-item');
        allItems.forEach((item) => {
            const fill = item.querySelector('.progress-fill');
            const title = item.querySelector('.feature-title');
            const icon = item.querySelector('svg');
            const description = item.querySelector('.feature-description');

            // Reset all visual states immediately
            fill.style.height = '0%';
            fill.style.transition = 'none'; // Disable transition for instant reset

            // Reset item states
            item.classList.remove('active', 'collapsing');
            title.style.color = '#6B7280'; // Gray-500
            icon.style.color = '#9CA3AF'; // Gray-400
            icon.classList.add('text-gray-400');
            description.classList.add('opacity-0');
        });

        // Force a reflow to ensure styles are applied
        document.body.offsetHeight;

        // Re-enable transitions
        setTimeout(() => {
            allItems.forEach((item) => {
                const fill = item.querySelector('.progress-fill');
                fill.style.transition = ''; // Restore original transition
            });

            // Show the current slide and restart
            showSlide(currentIndex);
            startSlideshow();
        }, 50);
    }

    // Add visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Additional safeguard for window focus events
    window.addEventListener('focus', () => {
        if (!document.hidden) {
            resetSlideshow();
        }
    });

    window.addEventListener('blur', () => {
        clearInterval(slideInterval);
    });

    // --- START ---
    initializeFeatureList();
    showSlide(currentIndex); // Show the first slide immediately
    startSlideshow(); // Start the automatic slideshow loop
}

// Demo button functionality for hero section
function initializeDemoButton() {
    const demoButton = document.querySelector('button[type="button"]');
    if (demoButton && demoButton.textContent.includes('Watch Demo')) {
        demoButton.addEventListener('click', () => {
            // Visual feedback for demo button
            const originalText = demoButton.innerHTML;
            demoButton.innerHTML = demoButton.innerHTML.replace('Watch Demo', '▶ Playing...');

            setTimeout(() => {
                demoButton.innerHTML = originalText;
            }, 2000);

            // In a real implementation, this would open a demo video or modal
            console.log('Demo video would play here');
        });
    }
}

// Add slideshow initialization to main DOMContentLoaded event
document.addEventListener('DOMContentLoaded', () => {
    initializeFeatureSlideshow();
    initializePricingAnimations();
    initializeFAQAccordion();
    initializeCTASection();
    initializeDemoButton();

    // Ensure scroll animations are initialized for all components
    setTimeout(() => {
        initializeScrollAnimations();
    }, 100);
});

// ========================================
// PRICING SECTION ANIMATIONS
// ========================================

function initializePricingAnimations() {
    // Check if Intersection Observer is supported
    if (!('IntersectionObserver' in window)) {
        // Fallback: trigger animations immediately
        triggerPricingAnimations();
        return;
    }

    // Intersection Observer options for pricing section
    const pricingObserverOptions = {
        root: null,
        rootMargin: '-20% 0px -20% 0px', // Trigger when section is well into view
        threshold: 0.3 // Trigger when 30% of the section is visible
    };

    // Create observer for pricing section with reset functionality
    const pricingObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Trigger pricing animations when section comes into view
                requestAnimationFrame(() => {
                    triggerPricingAnimations();
                });
            } else {
                // Reset pricing animations when section goes out of view
                requestAnimationFrame(() => {
                    resetPricingAnimations();
                });
            }
        });
    }, pricingObserverOptions);

    // Find and observe the pricing section
    const pricingSection = document.getElementById('pricing-section');
    if (pricingSection) {
        pricingObserver.observe(pricingSection);
    }
}

function triggerPricingAnimations() {
    const basicCard = document.querySelector('.pricing-card-basic');
    const enterpriseCard = document.querySelector('.pricing-card-enterprise');
    const proCard = document.querySelector('.pricing-card-pro');

    if (basicCard && enterpriseCard) {
        basicCard.classList.add('animate-closer');
        enterpriseCard.classList.add('animate-closer');
    }

    if (proCard) {
        proCard.classList.add('animate-grow');
    }
}

// ========================================
// FAQ ACCORDION FUNCTIONALITY
// ========================================

function initializeFAQAccordion() {
    // Find all accordion toggles
    const accordionToggles = document.querySelectorAll('.hs-accordion-toggle');

    accordionToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the accordion container and content
            const accordion = this.closest('.hs-accordion');
            const content = document.getElementById(this.getAttribute('aria-controls'));
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            // Close all other accordions
            accordionToggles.forEach(otherToggle => {
                if (otherToggle !== this) {
                    const otherAccordion = otherToggle.closest('.hs-accordion');
                    const otherContent = document.getElementById(otherToggle.getAttribute('aria-controls'));

                    // Close other accordion
                    otherToggle.setAttribute('aria-expanded', 'false');
                    otherAccordion.classList.remove('active');
                    otherContent.classList.add('hidden');
                    otherContent.style.height = '0px';
                }
            });

            // Toggle current accordion
            if (isExpanded) {
                // Close current accordion
                this.setAttribute('aria-expanded', 'false');
                accordion.classList.remove('active');
                content.style.height = content.scrollHeight + 'px';

                // Force reflow
                content.offsetHeight;

                // Animate to closed
                content.style.height = '0px';

                setTimeout(() => {
                    content.classList.add('hidden');
                }, 300);
            } else {
                // Open current accordion
                this.setAttribute('aria-expanded', 'true');
                accordion.classList.add('active');
                content.classList.remove('hidden');
                content.style.height = '0px';

                // Force reflow
                content.offsetHeight;

                // Animate to open
                content.style.height = content.scrollHeight + 'px';

                // Remove height after animation completes
                setTimeout(() => {
                    content.style.height = 'auto';
                }, 300);
            }
        });
    });

    // Initialize first accordion as open
    const firstAccordion = document.querySelector('.hs-accordion.active');
    if (firstAccordion) {
        const firstToggle = firstAccordion.querySelector('.hs-accordion-toggle');
        const firstContent = document.getElementById(firstToggle.getAttribute('aria-controls'));

        firstToggle.setAttribute('aria-expanded', 'true');
        firstContent.classList.remove('hidden');
        firstContent.style.height = 'auto';
    }
}

// ========================================
// CTA SECTION FUNCTIONALITY
// ========================================

function initializeCTASection() {
    const primaryBtn = document.querySelector('.cta-primary-btn');

    // Primary CTA Button - Start Free Trial
    if (primaryBtn) {
        primaryBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Store original content
            const originalContent = this.innerHTML;

            // Add loading state
            this.innerHTML = '<div class="flex items-center justify-center gap-2 w-full"><svg class="animate-spin shrink-0 size-4" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg><span>Starting Trial...</span></div>';
            this.style.pointerEvents = 'none';

            // Simulate trial start process
            setTimeout(() => {
                this.innerHTML = '<div class="flex items-center justify-center gap-2 w-full"><svg class="shrink-0 size-4 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/></svg><span>Trial Started!</span></div>';

                // Show success message
                showMessage('Free trial activated! Check your email for setup instructions.', 'success');

                // Reset button after delay
                setTimeout(() => {
                    this.innerHTML = originalContent;
                    this.style.pointerEvents = '';
                }, 2000);
            }, 2000);

            // Add ripple effect
            createCTARipple(this, e);
        });
    }
}

// Create ripple effect for CTA buttons
function createCTARipple(element, event) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('cta-ripple-effect');

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Show messages
function showMessage(message, type = 'success') {
    const messageEl = document.createElement('div');
    messageEl.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300`;

    if (type === 'success') {
        messageEl.className += ' bg-green-500 text-white';
    } else {
        messageEl.className += ' bg-blue-500 text-white';
    }

    messageEl.textContent = message;
    document.body.appendChild(messageEl);

    // Show message
    setTimeout(() => {
        messageEl.style.transform = 'translateX(0)';
    }, 100);

    // Hide message
    setTimeout(() => {
        messageEl.style.transform = 'translateX(100%)';
        setTimeout(() => {
            messageEl.remove();
        }, 300);
    }, 3000);
}


