<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Sentio - Your Intelligent E-commerce Platform</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lordicon Script for Animated Icons -->
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    
    <!-- Tailwind Configuration - Exact match with Testing.html -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    borderRadius: {
                        '4xl': '2rem',
                    },
                    colors: {
                        'brand': '#3674B5',
                        'brand-blue': '#3674B5'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'dm-sans': ['DM Sans', 'sans-serif'],
                        'dm-sans-medium': ['D<PERSON> Sans', 'sans-serif'],
                        'dm-sans-bold': ['DM Sans', 'sans-serif'],
                        'dm-sans-extrabold': ['DM Sans', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fade-in 0.6s ease-out',
                        'slide-up': 'slide-up 0.8s ease-out'
                    },
                    keyframes: {
                        'fade-in': {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        'slide-up': {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts: Inter + DM Sans - Exact match -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=DM+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- External CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <style>
        /* Blur fade animations with gentle upward motion */
        #welcomeText {
            opacity: 0;
            filter: blur(10px);
            transform: translateY(20px);
            animation: blurFadeIn 1s ease-out 0.3s forwards;
        }

        /* Blur fade in with gentle upward motion */
        @keyframes blurFadeIn {
            0% {
                opacity: 0;
                filter: blur(10px);
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                filter: blur(0px);
                transform: translateY(0);
            }
        }

        /* Blur fade out with gentle upward motion */
        @keyframes blurFadeOut {
            0% {
                opacity: 1;
                filter: blur(0px);
                transform: translateY(0);
            }
            100% {
                opacity: 0;
                filter: blur(10px);
                transform: translateY(-15px);
            }
        }

        /* Welcome container transitions */
        .welcome-container {
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .welcome-container.fade-out #welcomeText {
            animation: blurFadeOut 0.8s ease-out forwards;
        }

        /* Subtle glow effect during animation */
        #welcomeText::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 120%;
            height: 120%;
            background: radial-gradient(circle, rgba(54, 116, 181, 0.1) 0%, transparent 70%);
            transform: translate(-50%, -50%);
            opacity: 0;
            animation: glowPulse 3s ease-in-out 1.5s infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes glowPulse {
            0%, 100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            50% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }

        /* AI text styling and animation - trademark symbol style */
        .ai-text {
            color: #3674B5;
            font-size: 0.4em;
            opacity: 0;
            transform: translateY(5px) scale(0.8);
            animation: aiSlideIn 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) 1s forwards;
            display: inline-block;
            vertical-align: super;
            margin-left: 0.1em;
            font-weight: 700;
            letter-spacing: 0.02em;
            line-height: 1;
        }

        /* Responsive AI text sizing */
        @media (min-width: 640px) {
            .ai-text {
                font-size: 0.5em;
            }
        }

        /* AI slide in animation with bounce effect - trademark style */
        @keyframes aiSlideIn {
            0% {
                opacity: 0;
                transform: translateY(5px) scale(0.8);
            }
            60% {
                opacity: 1;
                transform: translateY(-1px) scale(1.05);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Enhanced welcome text container */
        .welcome-text-wrapper {
            position: relative;
            display: inline-block;
        }

        /* Mobile-specific improvements */
        @media (max-width: 640px) {
            .glass-card {
                margin-bottom: 1rem;
            }

            .option-card {
                min-height: 140px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            /* Improve touch targets */
            .option-card button {
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
            }

            /* Better spacing for mobile */
            .options-container {
                padding-top: 2rem;
                padding-bottom: 2rem;
            }
        }

        /* Options container reveal with staggered animation */
        .options-container {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
            filter: blur(8px);
            transition: all 0.9s cubic-bezier(0.16, 1, 0.3, 1);
            pointer-events: none; /* Prevent interaction when hidden */
        }

        .options-container.visible {
            opacity: 1;
            transform: translateY(0) scale(1);
            filter: blur(0px);
            pointer-events: auto; /* Enable interaction when visible */
        }

        /* Staggered card animations */
        .option-card {
            opacity: 0;
            transform: translateY(30px) scale(0.9);
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .options-container.visible .option-card:nth-child(1) {
            animation: cardSlideIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.2s forwards;
        }

        .options-container.visible .option-card:nth-child(2) {
            animation: cardSlideIn 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.4s forwards;
        }

        @keyframes cardSlideIn {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
            }
            60% {
                opacity: 0.8;
                transform: translateY(-5px) scale(1.02);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        /* Page transition with smooth blur exit */
        body.page-transition {
            opacity: 0;
            filter: blur(10px);
            transform: scale(0.95);
            transition: all 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Smooth blur exit for entire page */
        .page-blur-exit {
            opacity: 0;
            filter: blur(15px);
            transform: scale(0.9);
            transition: all 1.0s cubic-bezier(0.16, 1, 0.3, 1);
        }
        
        /* Google-style card design */
        .glass-card {
            background: #ffffff;
            border: 1px solid #e8eaed;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(60, 64, 67, 0.12), 0 1px 2px rgba(60, 64, 67, 0.24);
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        
        /* Google-style button design */
        .btn-primary {
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.25px;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            box-shadow: 0 1px 3px rgba(60, 64, 67, 0.12), 0 1px 2px rgba(60, 64, 67, 0.24);
        }

        .btn-primary:hover {
            background: #1557b0;
            box-shadow: 0 2px 6px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #1a73e8;
            border: 1px solid #dadce0;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.25px;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .btn-secondary:hover {
            background: #f8f9fa;
            border-color: #1a73e8;
            box-shadow: 0 1px 3px rgba(60, 64, 67, 0.12), 0 1px 2px rgba(60, 64, 67, 0.24);
            transform: translateY(-1px);
        }
        
        /* Google-style card hover effects */
        .option-card {
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .option-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(60, 64, 67, 0.15), 0 2px 4px rgba(60, 64, 67, 0.12);
        }

        /* Google-style focus states */
        .option-card:focus {
            outline: 2px solid #1a73e8;
            outline-offset: 2px;
        }

        /* Subtle background animation */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(54, 116, 181, 0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(54, 116, 181, 0.03) 0%, transparent 50%);
            animation: backgroundFloat 8s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes backgroundFloat {
            0%, 100% {
                transform: translateY(0) rotate(0deg);
                opacity: 0.5;
            }
            50% {
                transform: translateY(-10px) rotate(1deg);
                opacity: 0.8;
            }
        }
        
        /* Focus states for accessibility */
        .option-card:focus {
            outline: 2px solid #3674B5;
            outline-offset: 2px;
        }
        
        .option-card:focus:not(:focus-visible) {
            outline: none;
        }
        
        .option-card:focus-visible {
            outline: 2px solid #3674B5;
            outline-offset: 2px;
        }
    </style>
</head>
<body class="bg-white text-gray-800 font-inter overflow-hidden">
    
    <!-- Main Container -->
    <div class="min-h-screen relative">
        <!-- Welcome Message Container -->
        <div id="welcomeContainer" class="welcome-container absolute inset-0 flex items-center justify-center px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="welcome-text-wrapper">
                    <h1 id="welcomeText" class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-dm-sans-extrabold text-gray-800">
                        <!-- Text will be populated by JavaScript -->
                    </h1>
                </div>
            </div>
        </div>

        <!-- Options Container -->
        <div id="optionsContainer" class="options-container absolute inset-0 flex items-center justify-center px-4 sm:px-6 lg:px-8">
            <div class="max-w-4xl mx-auto w-full">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8 px-2 sm:px-4">
                
                <!-- Hear My Story Option -->
                <div class="glass-card option-card p-4 sm:p-6 text-left group cursor-pointer"
                     onclick="showStory()"
                     onkeydown="handleKeyPress(event, 'story')"
                     tabindex="0"
                     role="button"
                     aria-label="Learn about the technical story behind Sentio">

                    <div class="flex items-center mb-3 sm:mb-4">
                        <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-50 flex items-center justify-center mr-2 sm:mr-3">
                            <lord-icon
                                src="https://cdn.lordicon.com/hjrbjhnq.json"
                                trigger="hover"
                                state="hover-book"
                                colors="primary:#1a73e8,secondary:#1a73e8"
                                style="width:16px;height:16px;">
                            </lord-icon>
                        </div>
                        <h3 class="text-base sm:text-lg font-medium text-gray-900">Hear My Story</h3>
                    </div>

                    <p class="text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6 leading-relaxed">
                        Discover the technical journey behind Sentio - built with Tailwind CSS and vanilla JavaScript,
                        showcasing modern web development without heavy frameworks.
                    </p>

                    <button class="btn-secondary px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm w-full min-h-[44px]">
                        Learn More
                    </button>
                </div>
                
                <!-- Skip Ahead Option -->
                <div class="glass-card option-card p-4 sm:p-6 text-left group cursor-pointer"
                     onclick="skipToMain()"
                     onkeydown="handleKeyPress(event, 'skip')"
                     tabindex="0"
                     role="button"
                     aria-label="Skip to the main Sentio website experience">

                    <div class="flex items-center mb-3 sm:mb-4">
                        <div class="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-green-50 flex items-center justify-center mr-2 sm:mr-3">
                            <lord-icon
                                src="https://cdn.lordicon.com/rxgzsafd.json"
                                trigger="hover"
                                state="hover-arrow-right"
                                colors="primary:#1a73e8,secondary:#1a73e8"
                                style="width:16px;height:16px;">
                            </lord-icon>
                        </div>
                        <h3 class="text-base sm:text-lg font-medium text-gray-900">Skip Ahead</h3>
                    </div>

                    <p class="text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6 leading-relaxed">
                        Ready to explore? Jump straight into the full Sentio experience and discover
                        our intelligent e-commerce platform in action.
                    </p>

                    <button class="btn-primary text-white px-3 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm w-full min-h-[44px]">
                        Enter Sentio
                    </button>
                </div>

            </div>
        </div>

    </div>

    <script>
        // Animation timing and state management
        let animationComplete = false;

        // Simplified welcome text display with AI animation
        function animateWelcomeText() {
            const textElement = document.getElementById('welcomeText');

            // Set the text with AI positioned right after "Sentio" like a trademark
            textElement.innerHTML = 'Welcome to Sentio<span class="ai-text">AI</span>';

            // Show options after 4 seconds
            setTimeout(showOptions, 4000);
        }

        // Show options with coordinated transition
        function showOptions() {
            const welcomeContainer = document.getElementById('welcomeContainer');
            const optionsContainer = document.getElementById('optionsContainer');

            // Fade out welcome message
            welcomeContainer.classList.add('fade-out');

            // Show options after welcome fades
            setTimeout(() => {
                welcomeContainer.style.display = 'none';
                optionsContainer.classList.add('visible');
                animationComplete = true;
            }, 700);
        }

        // Navigate to story page with smooth blur transition
        function showStoryWithBlur() {
            // Add smooth blur exit effect to entire page
            document.body.classList.add('page-blur-exit');

            // Clear the logo animation flag to ensure it plays on story.html
            sessionStorage.removeItem('storyLogoAnimationPlayed');

            // Navigate to story.html after blur animation completes
            setTimeout(() => {
                window.location.href = 'story.html';
            }, 1000); // Wait for blur animation to complete
        }

        // Navigate to story page (main function)
        function showStory() {
            showStoryWithBlur();
        }

        // Skip to main website with smooth transition
        function skipToMain() {
            document.body.classList.add('page-transition');

            setTimeout(() => {
                window.location.href = 'Testing.html';
            }, 500);
        }

        // Keyboard accessibility
        function handleKeyPress(event, action) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                if (action === 'story') {
                    showStory();
                } else if (action === 'skip') {
                    skipToMain();
                }
            }
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Start welcome text animation immediately
            animateWelcomeText();

            // Accessibility: Allow immediate skip with Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape' && animationComplete) {
                    skipToMain();
                }
            });

            console.log('Sentio welcome page initialized successfully!');
        });

        // Handle reduced motion preference
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            // Skip animations for users who prefer reduced motion
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(showOptions, 100); // Show options immediately
            });
        }
    </script>

</body>
</html>
